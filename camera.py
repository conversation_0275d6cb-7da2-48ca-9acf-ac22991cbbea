'''
实验名称：摄像头使用
实验平台：01Studio CanMV K230
说明：实现摄像头图像采集显示
'''

import time, os, sys

from media.sensor import * #导入sensor模块，使用摄像头相关接口
from media.display import * #导入display模块，使用display相关接口
from media.media import * #导入media模块，使用meida相关接口

sensor = Sensor() #构建摄像头对象
sensor.reset() #复位和初始化摄像头
sensor.set_framesize(Sensor.FHD) #设置帧大小FHD(1920x1080)，默认通道0
sensor.set_pixformat(Sensor.RGB565) #设置输出图像格式，默认通道0

#使用IDE缓冲区输出图像,显示尺寸和sensor配置一致。
Display.init(Display.VIRT, sensor.width(), sensor.height())

MediaManager.init() #初始化media资源管理器

sensor.run() #启动sensor

clock = time.clock()

while True:

    ################
    ## 这里编写代码 ##
    ################
    clock.tick()

    img = sensor.snapshot() #拍摄一张图

    Display.show_image(img) #显示图片

    print(clock.fps()) #打印FPS

#import time
#from media.sensor import *
#from media.display import *
#from media.media import *

#sensor = Sensor()
#sensor.reset()
#sensor.set_framesize(width=800,height=480)
#sensor.set_pixformat(Sensor.GRAYSCALE)

#Display.init(Display.VIRT,sensor.width(),sensor.height())

#MediaManager.init()

#sensor.run()

#clock = time.clock()

#while True:
#    clock.tick()
#    img = sensor.snapshot()
##    img.draw_line(20, 20, 100, 20, color = (255, 0, 0), thickness = 2)

##    img.draw_rectangle(150, 20, 100, 30, color = (0, 255, 0), thickness = 2, fill = False)

##    img.draw_string_advanced(150, 180, 30, "Hello 01Studio", color = (255, 255, 255))
##    img.draw_string_advanced(40, 300, 30, "人生苦短, 我用Python", color = (255, 255, 255))
##    img.find_edges(image.EDGE_CANNY, threshold=(50, 80))
#    for l in img.find_line_segments(merge_distance = 0, max_theta_diff = 5):

#        img.draw_line(l.line(), color = (255, 0, 0), thickness=2)
#        print(l)
#    img.find_edges(image.EDGE_CANNY,threshold=(50,80))
#    Display.show_image(img)
#    print(clock.fps())
