# bilibili搜索学不会电磁场看教程
# 第12课，开始实践，做一个类似于2023年E题的激光点回位

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer

sensor = None

try:
    class PID:
        def __init__(self, kp, ki, input_value, target=320):
            self.e = 0
            self.e_last = 0
            self.kp = kp
            self.ki = ki
            self.target = target
            self.input_value = input_value
        def cal(self, value):
            self.e = self.target - value

            # 进行死区的控制
            if abs(self.e) < 5:  # 5像素死区
                return self.input_value

            # 增量型PID，这里用到的是PI
            delta = self.kp * (self.e-self.e_last) + self.ki * self.e
            self.e_last = self.e
            self.input_value = self.input_value + delta
            return self.input_value

    print("camera_test")

    sensor = Sensor()
    sensor.reset()

    # 鼠标悬停在函数上可以查看允许接收的参数
    sensor.set_framesize(width=800, height=480)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.VIRT,sensor.width(),sensor.height())
    # 初始化媒体管理器
    MediaManager.init()
    # 启动 sensor
    sensor.run()

    fpioa = FPIOA()
    #   fpioa.help()
    # 设置按键
    fpioa.set_function(21, FPIOA.GPIO21)
    key = Pin(21, Pin.IN, Pin.PULL_UP)

    #设置舵机和激光笔
    fpioa.set_function(2, FPIOA.GPIO2)
    fpioa.set_function(43, FPIOA.PWM1)
    fpioa.set_function(42, FPIOA.PWM0)
    pin = Pin(2, Pin.OUT)
    pin.value(1)

    clock = time.clock()
    pwm_y = PWM(0, 50)
    pwm_y.enable(1)

    pwm_x = PWM(1, 50)
    pwm_x.enable(1)

    c_x = 400
    c_y = 240
    # 控制舵机左右动
    pid_x = PID(-0.002, -0.0001, 1.5/20*100, c_x)
    #    pid_x = PID(0.0000, 0.0000, 1.62/20*100, c_x)

    # 控制舵机上下动
    pid_y = PID(0.002, 0.0001, 1.67/20*100, c_y)
    #    pid_y = PID(-0.0000, 0.0000, 1.49/20*100, c_y)

    threshold_dict = {'rect': [(81, 255)], 'red_point':\
    [(90, 100, -2, 32, -11, 7), (31, 100, 29, 127, -128, 127)]}
    center_point = []
    rect_points_lst = [[], [], [], []]

    def detect_red_point():
        global pin, img, red_x, red_y
        pin.value(1)
        img = sensor.snapshot(chn=CAM_CHN_ID_0)
#        img = img.copy(roi=cut_roi)
        blobs = img.find_blobs(threshold_dict['red_point'], False,\
                               x_stride=1, y_stride=1, \
                               pixels_threshold=20, margin=False)
        for blob in blobs:
            img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(), color=(0, 255, 0), thickness=2, fill=False)
            red_x = blob.x() + blob.w() / 2
            red_y = blob.y() + blob.h() / 2
        show_img_2_screen()

    def show_img_2_screen():
        global img
        if(img.height()>480 or img.width() > 800):
            scale = max(img.height() // 480, img.width() // 800) + 1
            img.midpoint_pool(scale, scale)
        img.compress_for_ide()
        Display.show_image(img)

    flag = -1
    counter_1_2_set = 0
    while True:
        clock.tick()

        img = sensor.snapshot() #拍摄一张图
        img.draw_string_advanced(50, 50, 20, "fps: {}".format(clock.fps()), color=(255, 0, 0))
        Display.show_image(img) #显示图片
        if key.value()==0:
            flag = (flag + 1) % 2

            # 创建一个空白图像
            img = image.Image(800, 480, image.RGB565)
            img.draw_rectangle(0, 0, 800, 480, color=(255, 255, 255), thickness=2, fill=True)
            img.draw_string_advanced(320, 240, 30, "switch_to_{}".format(flag), color=(0, 0, 0))

            show_img_2_screen()
            start_flag = 0


        last_calibration_time = 0
        if flag==0:
            img = sensor.snapshot()
            blobs = img.find_blobs(threshold_dict['red_point'], False,\
                                   x_stride=1, y_stride=1, \
                                   pixels_threshold=20, margin=False)
            red_x, red_y = None, None
            for blob in blobs:
                img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(), color=(0, 255, 0), thickness=2, fill=False)
                red_x = blob.x() + blob.w() / 2
                red_y = blob.y() + blob.h() / 2
            if not red_x == None:
                current_time = time.ticks_ms()
                if time.ticks_diff(current_time, last_calibration_time) > 1000:
                    if counter_1_2_set == 4:
                        center_point = [red_x, red_y]
                    else:
                        rect_points_lst[counter_1_2_set] = [red_x, red_y]

                    img.draw_string_advanced(20, 20, 30, "1_2_set_{}".format(counter_1_2_set), color=(0, 255, 0))
                    show_img_2_screen()
                    counter_1_2_set += 1
                    counter_1_2_set = counter_1_2_set % 5
                    last_calibration_time = current_time
                if key.value() == 0:
                    flag = 1
                    img.draw_string_advanced(30, 30, 30, "switch_to_{}".format(flag), color=(0, 0, 0))
                    show_img_2_screen()
                    time.sleep(1)
                    break
        elif flag == 1:
            detect_red_point()
            if not red_x == None:
                pid_x.target = center_point[0]
                pid_y.target = center_point[1]
                new_duty = pid_x.cal(red_x)
                if new_duty > 2.5/20*100:
                    new_duty = 2.5/20*100
                if new_duty < 0.5/20*100:
                    new_duty = 0.5/20*100
                pwm_x.duty(round(new_duty, 2))

                new_duty = pid_y.cal(red_y)
                if new_duty > 2.5/20*100:
                    new_duty = 2.5/20*100
                if new_duty < 0.5/20*100:
                    new_duty = 0.5/20*100
                pwm_y.duty(round(new_duty, 2))
        else:
            img = sensor.snapshot(chn=CAM_CHN_ID_0)
            img.draw_string_advanced(50, 50, 20, "fps: {}".format(clock.fps()), color=(255, 0, 0))
            img.compressed_for_ide()
            Display.show_image(img)
except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
