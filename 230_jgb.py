import time, os, sys

from media.sensor import * #导入sensor模块，使用摄像头相关接口
from media.display import * #导入display模块，使用display相关接口
from media.media import * #导入media模块，使用meida相关接口
from machine import FPIOA
from machine import Pin

fpioa = FPIOA()
fpioa.set_function(2,FPIOA.GPIO2)
pin = Pin(2,Pin.OUT)
pin.value(1)


sensor = Sensor() #构建摄像头对象
sensor.reset() #复位和初始化摄像头
sensor.set_framesize(width = 800,height = 480) #设置帧大小FHD(1920x1080)，默认通道0
sensor.set_pixformat(Sensor.RGB565) #设置输出图像格式，默认通道0

#使用IDE缓冲区输出图像,显示尺寸和sensor配置一致。
Display.init(Display.VIRT, sensor.width(), sensor.height())

MediaManager.init() #初始化media资源管理器

sensor.run() #启动sensor

clock = time.clock()

while True:

    ################
    ## 这里编写代码 ##
    ################
    clock.tick()

    img = sensor.snapshot() #拍摄一张图
    blobs = img.find_blobs([(71, 100, -16, 41, -6, 32),(12, 42, 16, 55, -31, 30)],merge = True,margin = 40)
    for blob in blobs:
        img.draw_rectangle(blob.rect(),color = (0,0,0,),thickness = 3)
#        img.draw_rectangle(blob.x(),blob.y(),blob.w(),blob.h(),color = (255,0,0,))
    img.draw_string_advanced(50,50,30,"fps:{}".format(clock.fps()))
    Display.show_image(img) #显示图片

#    print(clock.fps()) #打印FPS
