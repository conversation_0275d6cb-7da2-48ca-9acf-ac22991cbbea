from machine import FPIOA
from machine import Pin
from machine import PWM
from media.sensor import *
from media.display import *
from media.media import *
import time

fpioa = FPIOA()
fpioa.set_function(2,FPIOA.GPIO2)
fpioa.set_function(42,FPIOA.PWM0)
fpioa.set_function(43,FPIOA.PWM1)

sensor = Sensor()
sensor.reset()

sensor.set_framesize(width=800, height=480)
sensor.set_pixformat(Sensor.RGB565)

Display.init(Display.VIRT,sensor.width(),sensor.height())
# 初始化媒体管理器
MediaManager.init()
# 启动 sensor
sensor.run()
clock = time.clock()


pin = Pin(2,Pin.OUT)
pwm_s = PWM(0,50)
pwm_z = PWM(1,50)

# pwm_s增大会向下走，pwm_z增大会向左走
pin.value(1)
pwm_s.enable(1)
pwm_s.duty(1.69/20*100)
pwm_z.enable(1)
pwm_z.duty(1.49/20*100)

time.sleep(1)

while True:
    clock.tick()
    img = sensor.snapshot()
    img = img.copy(roi=(146, 10, 360, 352))
    img_rect = img.to_grayscale(copy=True)
    img_rect = img_rect.binary([(81, 255)])
    rects = img_rect.find_rects(threshold=10000)
    for rect in rects:
        corner = rect.corners()
        img.draw_line(corner[0][0], corner[0][1], corner[1][0], corner[1][1], color=(0, 255, 0), thickness=5)
        img.draw_line(corner[2][0], corner[2][1], corner[1][0], corner[1][1], color=(0, 255, 0), thickness=5)
        img.draw_line(corner[2][0], corner[2][1], corner[3][0], corner[3][1], color=(0, 255, 0), thickness=5)
        img.draw_line(corner[0][0], corner[0][1], corner[3][0], corner[3][1], color=(0, 255, 0), thickness=5)


    img.draw_string_advanced(50, 50, 20, "fps: {}".format(clock.fps()), color=(255, 0, 0))
    img.compressed_for_ide()
    Display.show_image(img)
