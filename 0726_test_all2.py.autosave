# bilibili搜索学不会电磁场看教程
# 第12课，开始实践，做一个类似于2023年E题的激光点回位

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer

sensor = None

try:
    class PID:
        def __init__(self, kp, ki, input_value, target=320):
            self.e = 0
            self.e_last = 0
            self.kp = kp
            self.ki = ki
            self.target = target
            self.input_value = input_value
        def cal(self, value):
            self.e = self.target - value

            # 进行死区的控制
            if abs(self.e) < 5:  # 5像素死区
                return self.input_value

            # 增量型PID，这里用到的是PI
            delta = self.kp * (self.e-self.e_last) + self.ki * self.e
            self.e_last = self.e
            self.input_value = self.input_value + delta
            return self.input_value
  
    print("camera_test")

    sensor = Sensor()
    sensor.reset()

    # 鼠标悬停在函数上可以查看允许接收的参数
    sensor.set_framesize(width=800, height=480)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.VIRT,sensor.width(),sensor.height())
    # 初始化媒体管理器
    MediaManager.init()
    # 启动 sensor
    sensor.run()

    fpioa = FPIOA()
    #   fpioa.help()
    # 设置按键
    fpioa.set_function(21, FPIOA.GPIO21)
    key = Pin(21, Pin.IN, Pin.PULL_UP)

    #设置舵机和激光笔
    fpioa.set_function(2, FPIOA.GPIO2)
    fpioa.set_function(43, FPIOA.PWM1)
    fpioa.set_function(42, FPIOA.PWM0)
    pin = Pin(2, Pin.OUT)
    pin.value(1)

    clock = time.clock()
    pwm_0 = PWM(0, 50)
    pwm_0.enable(1)
    
    pwm_1 = PWM(1, 50)
    pwm_1.enable(1)
    
    while True:
        clock.tick()
        img = sensor.snapshot()
        if key.value()==0:
            time.sleep_ms(2000)
            
            
        
    
    
except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
