from machine import FPIOA
from machine import Pin
from machine import PWM
import time

fpioa = FPIOA()
fpioa.set_function(2,FPIOA.GPIO2)
fpioa.set_function(42,FPIOA.PWM0)
fpioa.set_function(43,FPIOA.PWM1)
from media.sensor import * #导入sensor模块，使用摄像头相关接口
from media.display import * #导入display模块，使用display相关接口
from media.media import * #导入media模块，使用meida相关接口

pin = Pin(2,Pin.OUT)
pin.value(1)

sensor = Sensor() #构建摄像头对象
sensor.reset() #复位和初始化摄像头
sensor.set_framesize(width = 800,height  = 480) #设置帧大小FHD(1920x1080)，默认通道0
sensor.set_pixformat(Sensor.RGB565) #设置输出图像格式，默认通道0

#使用IDE缓冲区输出图像,显示尺寸和sensor配置一致。
Display.init(Display.VIRT, sensor.width(), sensor.height())

MediaManager.init() #初始化media资源管理器

sensor.run() #启动sensor

clock = time.clock()



# PWM0是上下，PWM1是左右
pwm_0 = PWM(0,50)
pwm_0.duty(1.7/20*100)
pwm_0.enable(1)

pwm_1 = PWM(1,50)
pwm_1.duty(1.4/20*100)
pwm_1.enable(1)

while True:
    pass
    clock.tick()

    img = sensor.snapshot() #拍摄一张图

    Display.show_image(img) #显示图片

    print(clock.fps()) #打印FPS
    pwm_0.duty(0.5/20*100)    # 0度 (0.5ms脉宽)
    pwm_1.duty(0.5/20*100)    # 0度
    time.sleep(5)      # 等待1秒

    pwm_0.duty(2.5/20*100)   # 180度 (2.5ms脉宽)
    pwm_1.duty(2.5/20*100)   # 180度
    time.sleep(5)      # 等待1秒
