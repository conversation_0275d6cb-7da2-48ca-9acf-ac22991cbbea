from machine import FPIOA
from machine import PWM
from machine import Pin
import time

fpioa = FPIOA()
fpioa.set_function(2,FPIOA.GPIO2)
pin = Pin(2,Pin.OUT)
pin.value(1)

fpioa.set_function(43,FPIOA.PWM1)
fpioa.set_function(42,FPIOA.PWM0)
pwm = PWM(1,50)
pwm_0 = PWM(0,50)

pwm.enable(1)
pwm_0.enable(1)

pwm_0.duty(1.6/20*100)
time.sleep(0.5)
pwm.duty(1.5/20*100)
time.sleep(0.5)

#pwm.deinit()
while True:
#    pass
    for i in range(47):
        pwm.duty((0.8+i/25)/20*100)
        pwm_0.duty((0.8+i/25)/20*100)
        time.sleep(0.1)
