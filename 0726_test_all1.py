# bilibili搜索学不会电磁场看教程
# 第12课，开始实践，做一个类似于2023年E题的激光点回位

import time
import os
import sys

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer

sensor = None

try:
    class PID:
        def __init__(self, kp, ki, input_value, target=320):
            self.e = 0
            self.e_last = 0
            self.kp = kp
            self.ki = ki
            self.target = target
            self.input_value = input_value
        def cal(self, value):
            self.e = self.target - value

            # 进行死区的控制
            if abs(self.e) < 5:  # 5像素死区
                return self.input_value

            # 增量型PID，这里用到的是PI
            delta = self.kp * (self.e-self.e_last) + self.ki * self.e
            self.e_last = self.e
            self.input_value = self.input_value + delta
            return self.input_value

    print("camera_test")

    sensor = Sensor()
    sensor.reset()

    # 鼠标悬停在函数上可以查看允许接收的参数
    sensor.set_framesize(width=800, height=480)
    sensor.set_pixformat(Sensor.RGB565)

    Display.init(Display.VIRT,sensor.width(),sensor.height())
    # 初始化媒体管理器
    MediaManager.init()
    # 启动 sensor
    sensor.run()

    fpioa = FPIOA()
    #   fpioa.help()
    # 设置按键
    fpioa.set_function(21, FPIOA.GPIO21)
    key = Pin(21, Pin.IN, Pin.PULL_UP)

    #设置舵机和激光笔
    fpioa.set_function(2, FPIOA.GPIO2)
    fpioa.set_function(43, FPIOA.PWM1)
    fpioa.set_function(42, FPIOA.PWM0)
    pin = Pin(2, Pin.OUT)
    pin.value(1)

    #上下转 减少值会向上
    pwm_0 = PWM(0, 50)
    pwm_0.enable(1)
    pwm_0.duty(1.67/20*100)


    #左右转
    pwm_1 = PWM(1, 50)
    pwm_1.enable(1)
    pwm_1.duty(1.49/20*100)


    clock = time.clock()

    flag = 0
    c_x = 400
    c_y = 240
    # 控制舵机左右动
    pid_x = PID(-0.002, -0.0001, 1.5/20*100, c_x)
#    pid_x = PID(0.0000, 0.0000, 1.62/20*100, c_x)

    # 控制舵机上下动
    pid_y = PID(0.002, 0.0001, 1.67/20*100, c_y)
#    pid_y = PID(-0.0000, 0.0000, 1.49/20*100, c_y)

    while True:
        clock.tick()
        os.exitpoint()
        img = sensor.snapshot()
#        img = img.copy(roi=(540, 300, 520, 520))
#        # 绘制方框，参数依次为：x, y, w, h, 颜色，线宽，是否填充
#        img.draw_rectangle(1000, 50, 300, 200, color=(0, 0, 255), thickness=4, fill=False)
        if key.value() == 0:
            time.sleep_ms(2000)
            for i in range(5):
                img = sensor.snapshot()
                img = img.copy(roi=(146, 10, 360, 352))

                #将彩色图像转化为灰度图像，copy=True意思是不覆盖原来的图像
                img_rect = img.to_grayscale(copy=True)
                img_rect = img_rect.binary([(81, 255)])
                rects = img_rect.find_rects(threshold=10000)

                if not rects == None:
                    for rect in rects:
                        corner = rect.corners()
                        img.draw_line(corner[0][0], corner[0][1], corner[1][0], corner[1][1], color=(0, 255, 0), thickness=5)
                        img.draw_line(corner[2][0], corner[2][1], corner[1][0], corner[1][1], color=(0, 255, 0), thickness=5)
                        img.draw_line(corner[2][0], corner[2][1], corner[3][0], corner[3][1], color=(0, 255, 0), thickness=5)
                        img.draw_line(corner[0][0], corner[0][1], corner[3][0], corner[3][1], color=(0, 255, 0), thickness=5)
                        c_x = sum([corner[k][0] for k in range(4)])/4
                        c_y = sum([corner[k][1] for k in range(4)])/4
                        print("x:{},y{}".format(c_x,c_y))
                if len(rects) == 2:
                    img.compressed_for_ide()
                    Display.show_image(img)
                    print("center_point: {}".format([round(c_x), round(c_y)]))
                    flag = 1
                    time.sleep_ms(3000)
                    pid_x.target = c_x
                    pid_y.target = c_y
                    break
            if flag == 0:
                print("识别错误")
        if flag == 1:
#            pin.value(1)
            img = sensor.snapshot()
            img = img.copy(roi=(146, 10, 360, 352))
            blobs = img.find_blobs([(90, 100, -2, 32, -11, 7), (31, 100, 29, 127, -128, 127)])
            for blob in blobs:
                img.draw_rectangle(blob.x(), blob.y(), blob.w(), blob.h(), color=(0, 255, 0), thickness=2, fill=False)
                c_x = blob.x() + blob.w() / 2
                c_y = blob.y() + blob.h() / 2
#                print("x:{},y{}".format(c_x,c_y))
                new_duty = pid_x.cal(c_x)
                if new_duty > 2.5/20*100:
                    new_duty = 2.5/20*100
                if new_duty < 0.5/20*100:
                    new_duty = 0.5/20*100
                pwm_1.enable(0)
                pwm_1.duty(round(new_duty, 2))

                pwm_1.enable(1)
                new_duty = pid_y.cal(c_y)
                if new_duty > 2.5/20*100:
                    new_duty = 2.5/20*100
                if new_duty < 0.5/20*100:
                    new_duty = 0.5/20*100
                pwm_0.enable(0)
                pwm_0.duty(round(new_duty, 2))
                print(round(new_duty, 1))
                pwm_0.enable(1)
                break

        img.draw_string_advanced(50, 50, 20, "fps: {}".format(clock.fps()), color=(255, 0, 0))
        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
